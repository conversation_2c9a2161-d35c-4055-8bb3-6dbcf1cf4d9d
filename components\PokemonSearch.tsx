'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { debounce } from '@/utils/debounce';

interface Pokemon {
  id: number;
  name: string;
  types: Array<{
    slot: number;
    type: {
      name: string;
      url: string;
    };
  }>;
  sprites: {
    front_default: string;
  };
}

interface PokemonSearchProps {
  onSearchResults: (results: Pokemon[]) => void;
  onLoadingChange: (loading: boolean) => void;
}

const PokemonSearch: React.FC<PokemonSearchProps> = ({ onSearchResults, onLoadingChange }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [allPokemon, setAllPokemon] = useState<Array<{ name: string; url: string }>>([]);
  const [error, setError] = useState<string | null>(null);
  
  // Usa useRef per mantenere la funzione debounced stabile
  const debouncedSearchRef = useRef<((term: string) => void) | null>(null);

  // Carica la lista di tutti i Pokemon all'avvio
  useEffect(() => {
    const fetchAllPokemon = async () => {
      try {
        const response = await fetch('https://pokeapi.co/api/v2/pokemon?limit=1000');
        const data = await response.json();
        setAllPokemon(data.results);
        setError(null);
      } catch (error) {
        console.error('Errore nel caricamento della lista Pokemon:', error);
        setError('Errore di connessione. Riprova più tardi.');
      }
    };

    fetchAllPokemon();
  }, []);

  // Funzione di ricerca stabile
  const performSearch = useCallback(async (term: string) => {
    if (!term.trim()) {
      onSearchResults([]);
      onLoadingChange(false);
      return;
    }

    onLoadingChange(true);

    try {
      // Filtra i Pokemon che corrispondono al termine di ricerca
      const filteredPokemon = allPokemon.filter(pokemon =>
        pokemon.name.toLowerCase().includes(term.toLowerCase())
      );

      // Limita i risultati a 20 per evitare troppe chiamate API
      const limitedResults = filteredPokemon.slice(0, 20);

      // Fetch dei dettagli per ogni Pokemon trovato
      const pokemonDetails = await Promise.all(
        limitedResults.map(async (pokemon) => {
          try {
            const response = await fetch(pokemon.url);
            const data = await response.json();
            return {
              id: data.id,
              name: data.name,
              types: data.types,
              sprites: data.sprites
            };
          } catch (error) {
            console.error(`Errore nel caricamento di ${pokemon.name}:`, error);
            return null;
          }
        })
      );

      // Filtra i risultati nulli
      const validResults = pokemonDetails.filter(pokemon => pokemon !== null) as Pokemon[];
      
      onSearchResults(validResults);
      setError(null);
    } catch (error) {
      console.error('Errore nella ricerca:', error);
      onSearchResults([]);
      setError('Errore durante la ricerca. Riprova.');
    } finally {
      onLoadingChange(false);
    }
  }, [allPokemon, onSearchResults, onLoadingChange]);

  // Crea la funzione debounced solo una volta
  useEffect(() => {
    if (allPokemon.length > 0) {
      debouncedSearchRef.current = debounce(performSearch, 500);
    }
  }, [allPokemon, performSearch]);

  // Effetto per la ricerca quando cambia il termine
  useEffect(() => {
    if (debouncedSearchRef.current) {
      debouncedSearchRef.current(searchTerm);
    }
  }, [searchTerm]);

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="relative">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Cerca un Pokemon..."
          className="font-body w-full px-4 py-4 pl-12 text-lg border-2 border-gray-200 rounded-2xl bg-white text-dark-grey placeholder-gray-400 focus:outline-none focus:border-poke-blue focus:ring-4 focus:ring-accent-sky/20 transition-all duration-300 shadow-lg"
          disabled={error !== null}
        />
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
          <svg
            className="w-6 h-6 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        {searchTerm && (
          <button
            onClick={() => setSearchTerm('')}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-battle-red transition-colors"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
      
      {error && (
        <div className="mt-4 p-4 bg-battle-red/10 border border-battle-red/20 rounded-lg">
          <p className="text-center text-battle-red font-body">
            {error}
          </p>
        </div>
      )}
      
      {searchTerm && !error && (
        <p className="text-center text-gray-500 text-sm mt-2 font-body">
          Risultati per: "{searchTerm}"
        </p>
      )}
    </div>
  );
};

export default PokemonSearch;
