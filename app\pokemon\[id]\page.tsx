'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageSelector from '@/components/LanguageSelector';

interface PokemonDetails {
  id: number;
  name: string;
  height: number;
  weight: number;
  base_experience: number;
  types: Array<{
    slot: number;
    type: {
      name: string;
      url: string;
    };
  }>;
  sprites: {
    front_default: string;
  };
  stats: Array<{
    base_stat: number;
    effort: number;
    stat: {
      name: string;
      url: string;
    };
  }>;
  abilities: Array<{
    ability: {
      name: string;
      url: string;
    };
    is_hidden: boolean;
    slot: number;
  }>;
  moves: Array<{
    move: {
      name: string;
      url: string;
    };
    version_group_details: Array<{
      level_learned_at: number;
      move_learn_method: {
        name: string;
        url: string;
      };
      version_group: {
        name: string;
        url: string;
      };
    }>;
  }>;
}

interface MoveDescription {
  name: string;
  description: string;
  level: number;
  versionGroup: string;
  learnMethod: string;
}

interface VersionGroup {
  name: string;
  displayName: string;
}

interface EncounterDetail {
  chance: number;
  max_level: number;
  min_level: number;
  method: {
    name: string;
    url: string;
  };
  condition_values: Array<{
    name: string;
    url: string;
  }>;
}

interface LocationEncounter {
  location_area: {
    name: string;
    url: string;
  };
  version_details: Array<{
    encounter_details: EncounterDetail[];
    max_chance: number;
    version: {
      name: string;
      url: string;
    };
  }>;
}

interface ProcessedEncounter {
  locationName: string;
  displayLocationName: string;
  encounters: Array<{
    method: string;
    chance: number;
    minLevel: number;
    maxLevel: number;
    conditions: string[];
  }>;
}

interface AbilityDescription {
  name: string;
  description: string;
  is_hidden: boolean;
  slot: number;
}

interface TypeEffectiveness {
  double_damage_from: Array<{ name: string; url: string }>;
  double_damage_to: Array<{ name: string; url: string }>;
  half_damage_from: Array<{ name: string; url: string }>;
  half_damage_to: Array<{ name: string; url: string }>;
  no_damage_from: Array<{ name: string; url: string }>;
  no_damage_to: Array<{ name: string; url: string }>;
}

interface EvolutionDetail {
  gender: any;
  held_item: any;
  item: any;
  known_move: any;
  known_move_type: any;
  location: any;
  min_affection: number | null;
  min_beauty: number | null;
  min_happiness: number | null;
  min_level: number | null;
  needs_overworld_rain: boolean;
  party_species: any;
  party_type: any;
  relative_physical_stats: any;
  time_of_day: string;
  trade_species: any;
  trigger: {
    name: string;
    url: string;
  };
  turn_upside_down: boolean;
}

interface EvolutionChainPokemon {
  evolution_details: EvolutionDetail[];
  evolves_to: EvolutionChainPokemon[];
  is_baby: boolean;
  species: {
    name: string;
    url: string;
  };
}

interface EvolutionChain {
  baby_trigger_item: any;
  chain: EvolutionChainPokemon;
  id: number;
}

interface ProcessedEvolution {
  name: string;
  displayName: string;
  sprite: string;
  minLevel: number | null;
  trigger: string;
  speciesUrl: string;
  pokemonId: number;
  stage: number;
  isCurrent: boolean;
}

// Mappa dei colori per i tipi di Pokemon
const typeColors: { [key: string]: string } = {
  normal: 'bg-gray-400',
  fire: 'bg-red-500',
  water: 'bg-blue-500',
  electric: 'bg-yellow-400',
  grass: 'bg-green-500',
  ice: 'bg-blue-200',
  fighting: 'bg-red-700',
  poison: 'bg-purple-500',
  ground: 'bg-yellow-600',
  flying: 'bg-indigo-400',
  psychic: 'bg-pink-500',
  bug: 'bg-green-400',
  rock: 'bg-yellow-800',
  ghost: 'bg-purple-700',
  dragon: 'bg-indigo-700',
  dark: 'bg-gray-800',
  steel: 'bg-gray-500',
  fairy: 'bg-pink-300',
};

// Mappa per i nomi delle statistiche
const statNames: { [key: string]: string } = {
  hp: 'HP',
  attack: 'Attack',
  defense: 'Defense',
  'special-attack': 'Sp. Attack',
  'special-defense': 'Sp. Defense',
  speed: 'Speed',
};

export default function PokemonDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { language } = useLanguage();
  const [pokemon, setPokemon] = useState<PokemonDetails | null>(null);
  const [moves, setMoves] = useState<MoveDescription[]>([]);
  const [allMoves, setAllMoves] = useState<MoveDescription[]>([]);
  const [encounters, setEncounters] = useState<ProcessedEncounter[]>([]);
  const [allEncounters, setAllEncounters] = useState<LocationEncounter[]>([]);
  const [versionGroups, setVersionGroups] = useState<VersionGroup[]>([]);
  const [selectedVersionGroup, setSelectedVersionGroup] = useState<string>('all');
  const [abilities, setAbilities] = useState<AbilityDescription[]>([]);
  const [typeEffectiveness, setTypeEffectiveness] = useState<{
    weaknesses: string[];
    resistances: string[];
    immunities: string[];
    strongAgainst: string[];
  }>({
    weaknesses: [],
    resistances: [],
    immunities: [],
    strongAgainst: []
  });
  const [evolutions, setEvolutions] = useState<ProcessedEvolution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Configurazione dei tab
  const tabs = [
    { id: 'overview', label: 'Panoramica', icon: '📊' },
    { id: 'abilities', label: 'Abilità', icon: '⚡' },
    { id: 'moves', label: 'Mosse', icon: '⚔️' },
    { id: 'encounters', label: 'Luoghi', icon: '📍' },
    { id: 'evolutions', label: 'Evoluzioni', icon: '🔄' },
    { id: 'effectiveness', label: 'Efficacia', icon: '🎯' }
  ];

  const capitalizeFirst = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  const formatVersionGroupName = (name: string): string => {
    // Mappa i nomi dei version groups a nomi più leggibili
    const nameMap: { [key: string]: string } = {
      'red-blue': 'Red/Blue',
      'yellow': 'Yellow',
      'gold-silver': 'Gold/Silver',
      'crystal': 'Crystal',
      'ruby-sapphire': 'Ruby/Sapphire',
      'emerald': 'Emerald',
      'firered-leafgreen': 'FireRed/LeafGreen',
      'diamond-pearl': 'Diamond/Pearl',
      'platinum': 'Platinum',
      'heartgold-soulsilver': 'HeartGold/SoulSilver',
      'black-white': 'Black/White',
      'black-2-white-2': 'Black 2/White 2',
      'x-y': 'X/Y',
      'omega-ruby-alpha-sapphire': 'Omega Ruby/Alpha Sapphire',
      'sun-moon': 'Sun/Moon',
      'ultra-sun-ultra-moon': 'Ultra Sun/Ultra Moon',
      'lets-go-pikachu-lets-go-eevee': 'Let\'s Go Pikachu/Eevee',
      'sword-shield': 'Sword/Shield',
      'brilliant-diamond-and-shining-pearl': 'Brilliant Diamond/Shining Pearl',
      'legends-arceus': 'Legends Arceus',
      'scarlet-violet': 'Scarlet/Violet'
    };
    
    return nameMap[name] || name.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatLocationName = (name: string): string => {
    return name.split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
      .replace(/Area$/, '')
      .replace(/\s+/g, ' ')
      .trim();
  };

  const formatEncounterMethod = (method: string): string => {
    const methodMap: { [key: string]: string } = {
      'walk': 'Camminando',
      'surf': 'Surfando',
      'old-rod': 'Canna Vecchia',
      'good-rod': 'Canna Buona',
      'super-rod': 'Super Canna',
      'rock-smash': 'Spaccaroccia',
      'headbutt': 'Bottintesta',
      'dark-grass': 'Erba Alta',
      'grass-spots': 'Macchie d\'erba',
      'cave-spots': 'Macchie nella grotta',
      'bridge-spots': 'Sul ponte',
      'super-rod-spots': 'Super Canna (macchie)',
      'surf-spots': 'Surf (macchie)',
      'gift': 'Regalo',
      'sos-encounter': 'Chiamata SOS',
      'roaming': 'Vagante'
    };
    
    return methodMap[method] || method.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatCondition = (condition: string): string => {
    const conditionMap: { [key: string]: string } = {
      'time-morning': 'Mattina',
      'time-day': 'Giorno', 
      'time-night': 'Notte',
      'season-spring': 'Primavera',
      'season-summer': 'Estate',
      'season-autumn': 'Autunno',
      'season-winter': 'Inverno',
      'swarm-yes': 'Sciame',
      'swarm-no': 'Normale',
      'radar-on': 'Poké Radar',
      'slot2-ruby': 'Ruby inserito',
      'slot2-sapphire': 'Sapphire inserito',
      'slot2-emerald': 'Emerald inserito',
      'slot2-firered': 'FireRed inserito',
      'slot2-leafgreen': 'LeafGreen inserito'
    };
    
    return conditionMap[condition] || condition.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatEvolutionTrigger = (trigger: string): string => {
    const triggerMap: { [key: string]: string } = {
      'level-up': 'Livello',
      'trade': 'Scambio',
      'use-item': 'Oggetto',
      'shed': 'Spargimento',
      'spin': 'Rotazione',
      'tower-of-darkness': 'Torre delle Tenebre',
      'tower-of-waters': 'Torre delle Acque',
      'three-critical-hits': 'Tre colpi critici',
      'take-damage': 'Subire danno',
      'other': 'Altro'
    };
    
    return triggerMap[trigger] || trigger.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const extractPokemonIdFromUrl = (url: string): number => {
    const matches = url.match(/\/pokemon-species\/(\d+)\//);
    return matches ? parseInt(matches[1]) : 0;
  };

  // Fetch dei dettagli del Pokemon
  useEffect(() => {
    const fetchPokemonDetails = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`https://pokeapi.co/api/v2/pokemon/${params.id}`);
        
        if (!response.ok) {
          throw new Error('Pokemon non trovato');
        }
        
        const data = await response.json();
        setPokemon(data);
      } catch (error) {
        console.error('Errore nel caricamento del Pokemon:', error);
        setError('Errore nel caricamento del Pokemon');
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchPokemonDetails();
    }
  }, [params.id]);

  // Fetch delle descrizioni delle abilità
  useEffect(() => {
    const fetchAbilityDescriptions = async () => {
      if (!pokemon) return;

      try {
        const abilitiesWithDescriptions = await Promise.all(
          pokemon.abilities.map(async (abilityData) => {
            try {
              const abilityResponse = await fetch(abilityData.ability.url);
              const abilityDetails = await abilityResponse.json();
              
              const flavorText = abilityDetails.flavor_text_entries.find((entry: any) => 
                entry.language.name === language
              );
              
              const fallbackText = abilityDetails.flavor_text_entries.find((entry: any) => 
                entry.language.name === 'en'
              );
              
              const description = flavorText?.flavor_text || fallbackText?.flavor_text || 'Descrizione non disponibile';
              
              return {
                name: capitalizeFirst(abilityData.ability.name.replace('-', ' ')),
                description: description.replace(/\n/g, ' ').replace(/\f/g, ' '),
                is_hidden: abilityData.is_hidden,
                slot: abilityData.slot,
              };
            } catch (error) {
              console.error(`Errore nel caricamento dell'abilità ${abilityData.ability.name}:`, error);
              return {
                name: capitalizeFirst(abilityData.ability.name.replace('-', ' ')),
                description: 'Descrizione non disponibile',
                is_hidden: abilityData.is_hidden,
                slot: abilityData.slot,
              };
            }
          })
        );

        setAbilities(abilitiesWithDescriptions);
      } catch (error) {
        console.error('Errore nel caricamento delle abilità:', error);
      }
    };

    fetchAbilityDescriptions();
  }, [pokemon, language]);

  // Fetch dei punti di forza e debolezze basati sui tipi
  useEffect(() => {
    const fetchTypeEffectiveness = async () => {
      if (!pokemon) return;

      try {
        const typeData = await Promise.all(
          pokemon.types.map(async (typeInfo) => {
            const response = await fetch(typeInfo.type.url);
            return await response.json();
          })
        );

        let weaknesses = new Set<string>();
        let resistances = new Set<string>();
        let immunities = new Set<string>();
        let strongAgainst = new Set<string>();

        typeData.forEach((type) => {
          const damageRelations = type.damage_relations;
          
          damageRelations.double_damage_from.forEach((t: any) => {
            weaknesses.add(t.name);
          });
          
          damageRelations.half_damage_from.forEach((t: any) => {
            resistances.add(t.name);
          });
          
          damageRelations.no_damage_from.forEach((t: any) => {
            immunities.add(t.name);
          });
          
          damageRelations.double_damage_to.forEach((t: any) => {
            strongAgainst.add(t.name);
          });
        });

        if (pokemon.types.length === 2) {
          immunities.forEach(immunity => {
            weaknesses.delete(immunity);
            resistances.delete(immunity);
          });
          
          const weaknessArray = Array.from(weaknesses);
          const resistanceArray = Array.from(resistances);
          
          weaknessArray.forEach(weakness => {
            if (resistanceArray.includes(weakness)) {
              weaknesses.delete(weakness);
              resistances.delete(weakness);
            }
          });
        }

        setTypeEffectiveness({
          weaknesses: Array.from(weaknesses),
          resistances: Array.from(resistances),
          immunities: Array.from(immunities),
          strongAgainst: Array.from(strongAgainst)
        });
      } catch (error) {
        console.error('Errore nel caricamento dell\'efficacia dei tipi:', error);
      }
    };

    fetchTypeEffectiveness();
  }, [pokemon]);

  // Fetch dei luoghi di incontro
  useEffect(() => {
    const fetchEncounters = async () => {
      if (!pokemon) return;

      try {
        const response = await fetch(`https://pokeapi.co/api/v2/pokemon/${pokemon.id}/encounters`);
        const encounterData: LocationEncounter[] = await response.json();
        setAllEncounters(encounterData);
      } catch (error) {
        console.error('Errore nel caricamento dei luoghi di incontro:', error);
      }
    };

    fetchEncounters();
  }, [pokemon]);

  // Fetch della catena evolutiva
  useEffect(() => {
    const fetchEvolutionChain = async () => {
      if (!pokemon) return;

      try {
        // Primo step: ottenere le informazioni della specie
        const speciesResponse = await fetch(`https://pokeapi.co/api/v2/pokemon-species/${pokemon.id}`);
        if (!speciesResponse.ok) {
          console.error('Errore nel fetch della specie');
          return;
        }
        const speciesData = await speciesResponse.json();

        // Secondo step: ottenere la catena evolutiva
        const evolutionResponse = await fetch(speciesData.evolution_chain.url);
        if (!evolutionResponse.ok) {
          console.error('Errore nel fetch della catena evolutiva');
          return;
        }
        const evolutionData: EvolutionChain = await evolutionResponse.json();

        // Terzo step: processare TUTTA la catena evolutiva in ordine sequenziale
        const processedEvolutions: ProcessedEvolution[] = [];

        const processEvolutionChain = async (chainPokemon: EvolutionChainPokemon, stage: number = 0) => {
          const pokemonId = extractPokemonIdFromUrl(chainPokemon.species.url);

          try {
            // Ottenere sprite del Pokemon
            const pokemonResponse = await fetch(`https://pokeapi.co/api/v2/pokemon/${chainPokemon.species.name}`);
            if (pokemonResponse.ok) {
              const pokemonData = await pokemonResponse.json();

              // Determinare i dettagli dell'evoluzione
              let evolutionDetail = null;
              let minLevel = null;
              let trigger = stage === 0 ? 'Forma base' : 'Livello';

              if (chainPokemon.evolution_details && chainPokemon.evolution_details.length > 0) {
                evolutionDetail = chainPokemon.evolution_details[0];
                minLevel = evolutionDetail.min_level;
                trigger = formatEvolutionTrigger(evolutionDetail.trigger.name);
              }

              processedEvolutions.push({
                name: chainPokemon.species.name,
                displayName: capitalizeFirst(chainPokemon.species.name),
                sprite: pokemonData.sprites.front_default || '',
                minLevel: minLevel,
                trigger: trigger,
                speciesUrl: chainPokemon.species.url,
                pokemonId: pokemonId,
                stage: stage,
                isCurrent: chainPokemon.species.name === pokemon.name
              });
            }
          } catch (error) {
            console.error(`Errore nel caricamento dell'evoluzione ${chainPokemon.species.name}:`, error);
          }

          // Processare ricorsivamente le evoluzioni successive
          if (chainPokemon.evolves_to && chainPokemon.evolves_to.length > 0) {
            for (const evolution of chainPokemon.evolves_to) {
              await processEvolutionChain(evolution, stage + 1);
            }
          }
        };

        // Inizia dalla radice della catena
        await processEvolutionChain(evolutionData.chain);

        // Ordina le evoluzioni per stage
        processedEvolutions.sort((a, b) => a.stage - b.stage);

        console.log('Evoluzioni processate:', processedEvolutions);
        setEvolutions(processedEvolutions);
      } catch (error) {
        console.error('Errore nel caricamento della catena evolutiva:', error);
      }
    };

    fetchEvolutionChain();
  }, [pokemon]);

  // Fetch delle descrizioni delle mosse
  useEffect(() => {
    const fetchMoveDescriptions = async () => {
      if (!pokemon) return;

      try {
        const versionGroupsSet = new Set<string>();
        
        pokemon.moves.forEach(moveData => {
          moveData.version_group_details.forEach(detail => {
            versionGroupsSet.add(detail.version_group.name);
          });
        });

        allEncounters.forEach(locationData => {
          locationData.version_details.forEach(versionDetail => {
            const versionToGroup: { [key: string]: string } = {
              'red': 'red-blue',
              'blue': 'red-blue', 
              'yellow': 'yellow',
              'gold': 'gold-silver',
              'silver': 'gold-silver',
              'crystal': 'crystal',
              'ruby': 'ruby-sapphire',
              'sapphire': 'ruby-sapphire',
              'emerald': 'emerald',
              'firered': 'firered-leafgreen',
              'leafgreen': 'firered-leafgreen',
              'diamond': 'diamond-pearl',
              'pearl': 'diamond-pearl',
              'platinum': 'platinum',
              'heartgold': 'heartgold-soulsilver',
              'soulsilver': 'heartgold-soulsilver',
              'black': 'black-white',
              'white': 'black-white',
              'black-2': 'black-2-white-2',
              'white-2': 'black-2-white-2',
              'x': 'x-y',
              'y': 'x-y',
              'omega-ruby': 'omega-ruby-alpha-sapphire',
              'alpha-sapphire': 'omega-ruby-alpha-sapphire',
              'sun': 'sun-moon',
              'moon': 'sun-moon',
              'ultra-sun': 'ultra-sun-ultra-moon',
              'ultra-moon': 'ultra-sun-ultra-moon',
              'lets-go-pikachu': 'lets-go-pikachu-lets-go-eevee',
              'lets-go-eevee': 'lets-go-pikachu-lets-go-eevee',
              'sword': 'sword-shield',
              'shield': 'sword-shield',
              'brilliant-diamond': 'brilliant-diamond-and-shining-pearl',
              'shining-pearl': 'brilliant-diamond-and-shining-pearl',
              'legends-arceus': 'legends-arceus',
              'scarlet': 'scarlet-violet',
              'violet': 'scarlet-violet'
            };
            
            const groupName = versionToGroup[versionDetail.version.name] || versionDetail.version.name;
            versionGroupsSet.add(groupName);
          });
        });

        const versionGroupsArray: VersionGroup[] = [
          { name: 'all', displayName: 'Tutte le versioni' },
          ...Array.from(versionGroupsSet).map(vg => ({
            name: vg,
            displayName: formatVersionGroupName(vg)
          })).sort((a, b) => a.displayName.localeCompare(b.displayName))
        ];
        
        setVersionGroups(versionGroupsArray);

        const movesWithDescriptions = await Promise.all(
          pokemon.moves.map(async (moveData) => {
            try {
              const moveResponse = await fetch(moveData.move.url);
              const moveDetails = await moveResponse.json();
              
              const flavorText = moveDetails.flavor_text_entries.find((entry: any) => 
                entry.language.name === language
              );
              
              const fallbackText = moveDetails.flavor_text_entries.find((entry: any) => 
                entry.language.name === 'en'
              );
              
              const description = flavorText?.flavor_text || fallbackText?.flavor_text || 'Descrizione non disponibile';
              
              return moveData.version_group_details.map(detail => ({
                name: capitalizeFirst(moveData.move.name.replace('-', ' ')),
                description: description.replace(/\n/g, ' ').replace(/\f/g, ' '),
                level: detail.level_learned_at,
                versionGroup: detail.version_group.name,
                learnMethod: detail.move_learn_method.name,
              }));
            } catch (error) {
              console.error(`Errore nel caricamento della mossa ${moveData.move.name}:`, error);
              return [];
            }
          })
        );

        const flattenedMoves = movesWithDescriptions.flat().filter(move => move !== null);
        setAllMoves(flattenedMoves);
        
        const levelUpMoves = flattenedMoves
          .filter(move => move.learnMethod === 'level-up')
          .sort((a, b) => a.level - b.level);
        
        setMoves(levelUpMoves);
      } catch (error) {
        console.error('Errore nel caricamento delle mosse:', error);
      }
    };

    fetchMoveDescriptions();
  }, [pokemon, language, allEncounters]);

  // Filtra le mosse e gli encounters in base al version group selezionato
  useEffect(() => {
    if (selectedVersionGroup === 'all') {
      const uniqueMoves = allMoves
        .filter(move => move.learnMethod === 'level-up')
        .reduce((acc, current) => {
          const exists = acc.find(item => item.name === current.name);
          if (!exists || (current.level < exists.level && current.level > 0)) {
            return acc.filter(item => item.name !== current.name).concat([current]);
          }
          return acc;
        }, [] as MoveDescription[])
        .sort((a, b) => a.level - b.level);
      
      setMoves(uniqueMoves);
    } else {
      const filteredMoves = allMoves
        .filter(move => move.versionGroup === selectedVersionGroup && move.learnMethod === 'level-up')
        .sort((a, b) => a.level - b.level);
      
      setMoves(filteredMoves);
    }

    // Filtra gli encounters
    const processEncounters = () => {
      if (selectedVersionGroup === 'all') {
        const processedEncounters: ProcessedEncounter[] = [];
        
        allEncounters.forEach(locationData => {
          const combinedEncounters: { [key: string]: EncounterDetail[] } = {};
          
          locationData.version_details.forEach(versionDetail => {
            const method = versionDetail.encounter_details[0]?.method.name || 'unknown';
            if (!combinedEncounters[method]) {
              combinedEncounters[method] = [];
            }
            combinedEncounters[method].push(...versionDetail.encounter_details);
          });

          if (Object.keys(combinedEncounters).length > 0) {
            const encounters = Object.entries(combinedEncounters).map(([method, details]) => {
              const totalChance = details.reduce((sum, detail) => sum + detail.chance, 0);
              const levels = details.map(d => ({ min: d.min_level, max: d.max_level }));
              const minLevel = Math.min(...levels.map(l => l.min));
              const maxLevel = Math.max(...levels.map(l => l.max));
              const conditions = [...new Set(details.flatMap(d => d.condition_values.map(c => c.name)))];

              return {
                method: formatEncounterMethod(method),
                chance: Math.round(totalChance / details.length),
                minLevel,
                maxLevel,
                conditions: conditions.map(formatCondition)
              };
            });

            processedEncounters.push({
              locationName: locationData.location_area.name,
              displayLocationName: formatLocationName(locationData.location_area.name),
              encounters
            });
          }
        });

        setEncounters(processedEncounters);
      } else {
        const processedEncounters: ProcessedEncounter[] = [];
        
        const groupToVersions: { [key: string]: string[] } = {
          'red-blue': ['red', 'blue'],
          'yellow': ['yellow'],
          'gold-silver': ['gold', 'silver'],
          'crystal': ['crystal'],
          'ruby-sapphire': ['ruby', 'sapphire'],
          'emerald': ['emerald'],
          'firered-leafgreen': ['firered', 'leafgreen'],
          'diamond-pearl': ['diamond', 'pearl'],
          'platinum': ['platinum'],
          'heartgold-soulsilver': ['heartgold', 'soulsilver'],
          'black-white': ['black', 'white'],
          'black-2-white-2': ['black-2', 'white-2'],
          'x-y': ['x', 'y'],
          'omega-ruby-alpha-sapphire': ['omega-ruby', 'alpha-sapphire'],
          'sun-moon': ['sun', 'moon'],
          'ultra-sun-ultra-moon': ['ultra-sun', 'ultra-moon'],
          'lets-go-pikachu-lets-go-eevee': ['lets-go-pikachu', 'lets-go-eevee'],
          'sword-shield': ['sword', 'shield'],
          'brilliant-diamond-and-shining-pearl': ['brilliant-diamond', 'shining-pearl'],
          'legends-arceus': ['legends-arceus'],
          'scarlet-violet': ['scarlet', 'violet']
        };

        const targetVersions = groupToVersions[selectedVersionGroup] || [selectedVersionGroup];

        allEncounters.forEach(locationData => {
          const relevantVersions = locationData.version_details.filter(versionDetail => 
            targetVersions.includes(versionDetail.version.name)
          );

          if (relevantVersions.length > 0) {
            const encounters = relevantVersions.flatMap(versionDetail => 
              versionDetail.encounter_details.map(detail => ({
                method: formatEncounterMethod(detail.method.name),
                chance: detail.chance,
                minLevel: detail.min_level,
                maxLevel: detail.max_level,
                conditions: detail.condition_values.map(c => formatCondition(c.name))
              }))
            );

            const groupedEncounters: { [key: string]: typeof encounters } = {};
            encounters.forEach(encounter => {
              if (!groupedEncounters[encounter.method]) {
                groupedEncounters[encounter.method] = [];
              }
              groupedEncounters[encounter.method].push(encounter);
            });

            const finalEncounters = Object.entries(groupedEncounters).map(([method, details]) => ({
              method,
              chance: Math.max(...details.map(d => d.chance)),
              minLevel: Math.min(...details.map(d => d.minLevel)),
              maxLevel: Math.max(...details.map(d => d.maxLevel)),
              conditions: [...new Set(details.flatMap(d => d.conditions))]
            }));

            processedEncounters.push({
              locationName: locationData.location_area.name,
              displayLocationName: formatLocationName(locationData.location_area.name),
              encounters: finalEncounters
            });
          }
        });

        setEncounters(processedEncounters);
      }
    };

    processEncounters();
  }, [selectedVersionGroup, allMoves, allEncounters]);

  // Funzione per renderizzare il contenuto dei tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-8">
            {/* Statistiche */}
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Statistiche</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {pokemon?.stats.map((stat, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-600">
                        {statNames[stat.stat.name] || stat.stat.name}
                      </span>
                      <span className="text-lg font-bold text-gray-800">{stat.base_stat}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${Math.min((stat.base_stat / 255) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Info generali */}
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Informazioni generali</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">ID Pokédex:</span>
                    <span className="font-semibold">#{pokemon?.id.toString().padStart(3, '0')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Altezza:</span>
                    <span className="font-semibold">{pokemon ? (pokemon.height / 10).toFixed(1) : 0} m</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Peso:</span>
                    <span className="font-semibold">{pokemon ? (pokemon.weight / 10).toFixed(1) : 0} kg</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Esperienza base:</span>
                    <span className="font-semibold">{pokemon?.base_experience || 'N/A'}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 block mb-2">Tipi:</span>
                    <div className="flex gap-2">
                      {pokemon?.types.map((type, index) => (
                        <span
                          key={index}
                          className={`px-3 py-1 rounded-full text-white text-sm font-medium ${
                            typeColors[type.type.name] || 'bg-gray-400'
                          }`}
                        >
                          {capitalizeFirst(type.type.name)}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'abilities':
        return (
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Abilità del Pokemon</h3>
            {abilities.length > 0 ? (
              <div className="space-y-4">
                {abilities.map((ability, index) => (
                  <div key={index} className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-semibold text-gray-800 text-lg">{ability.name}</h4>
                      <div className="flex gap-2">
                        <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          Slot {ability.slot}
                        </span>
                        {ability.is_hidden && (
                          <span className="text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                            Nascosta
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">{ability.description}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400 mx-auto mb-4"></div>
                <p className="text-gray-600">Caricamento abilità...</p>
              </div>
            )}
          </div>
        );

      case 'moves':
        return (
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Mosse del Pokemon</h3>

            {moves.length > 0 ? (
              <div className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  {selectedVersionGroup === 'all' 
                    ? `Mostrando ${moves.length} mosse apprese per livello da tutte le versioni`
                    : `Mostrando ${moves.length} mosse apprese per livello in ${versionGroups.find(vg => vg.name === selectedVersionGroup)?.displayName}`
                  }
                </div>
                {moves.map((move, index) => (
                  <div key={`${move.name}-${move.versionGroup}-${index}`} className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold text-gray-800 text-lg">{move.name}</h4>
                      <div className="flex flex-col items-end gap-1">
                        <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          Livello {move.level || 'N/A'}
                        </span>
                        {selectedVersionGroup === 'all' && (
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                            {formatVersionGroupName(move.versionGroup)}
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">{move.description}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                {allMoves.length === 0 ? (
                  <>
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400 mx-auto mb-4"></div>
                    <p className="text-gray-600">Caricamento mosse...</p>
                  </>
                ) : (
                  <p className="text-gray-600">Nessuna mossa disponibile per la versione selezionata.</p>
                )}
              </div>
            )}
          </div>
        );

      case 'encounters':
        return (
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Luoghi di incontro</h3>
            
            {encounters.length > 0 ? (
              <div className="space-y-6">
                <div className="text-sm text-gray-600 mb-4">
                  {selectedVersionGroup === 'all' 
                    ? `Mostrando luoghi di incontro da tutte le versioni`
                    : `Mostrando luoghi di incontro in ${versionGroups.find(vg => vg.name === selectedVersionGroup)?.displayName}`
                  }
                </div>
                
                {encounters.map((location, locationIndex) => (
                  <div key={locationIndex} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      {location.displayLocationName}
                    </h4>
                    
                    <div className="grid gap-3">
                      {location.encounters.map((encounter, encounterIndex) => (
                        <div key={encounterIndex} className="bg-gray-50 rounded-lg p-3">
                          <div className="flex flex-wrap items-center gap-3 mb-2">
                            <span className="text-sm font-medium text-gray-700">
                              {encounter.method}
                            </span>
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                              {encounter.chance}% probabilità
                            </span>
                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                              Livello {encounter.minLevel === encounter.maxLevel 
                                ? encounter.minLevel 
                                : `${encounter.minLevel}-${encounter.maxLevel}`
                              }
                            </span>
                          </div>
                          
                          {encounter.conditions.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {encounter.conditions.map((condition, condIndex) => (
                                <span 
                                  key={condIndex} 
                                  className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full"
                                >
                                  {condition}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : allEncounters.length === 0 ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400 mx-auto mb-4"></div>
                <p className="text-gray-600">Caricamento luoghi di incontro...</p>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">
                  {selectedVersionGroup === 'all' 
                    ? 'Nessun luogo di incontro disponibile.'
                    : `Nessun luogo di incontro disponibile per ${versionGroups.find(vg => vg.name === selectedVersionGroup)?.displayName}.`
                  }
                </p>
              </div>
            )}
          </div>
        );

      case 'effectiveness':
        return (
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-6">Efficacia dei tipi</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Debolezze */}
              <div>
                <h4 className="text-lg font-semibold text-red-700 mb-3 flex items-center gap-2">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  Debolezze (2x danno)
                </h4>
                <div className="flex flex-wrap gap-2">
                  {typeEffectiveness.weaknesses.length > 0 ? (
                    typeEffectiveness.weaknesses.map((type, index) => (
                      <span
                        key={index}
                        className={`px-3 py-1 rounded-full text-white text-sm font-medium ${
                          typeColors[type] || 'bg-gray-400'
                        }`}
                      >
                        {capitalizeFirst(type)}
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-500 text-sm">Nessuna debolezza particolare</span>
                  )}
                </div>
              </div>

              {/* Resistenze */}
              <div>
                <h4 className="text-lg font-semibold text-green-700 mb-3 flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  Resistenze (0.5x danno)
                </h4>
                <div className="flex flex-wrap gap-2">
                  {typeEffectiveness.resistances.length > 0 ? (
                    typeEffectiveness.resistances.map((type, index) => (
                      <span
                        key={index}
                        className={`px-3 py-1 rounded-full text-white text-sm font-medium ${
                          typeColors[type] || 'bg-gray-400'
                        }`}
                      >
                        {capitalizeFirst(type)}
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-500 text-sm">Nessuna resistenza particolare</span>
                  )}
                </div>
              </div>

              {/* Immunità */}
              {typeEffectiveness.immunities.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-blue-700 mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    Immunità (0x danno)
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {typeEffectiveness.immunities.map((type, index) => (
                      <span
                        key={index}
                        className={`px-3 py-1 rounded-full text-white text-sm font-medium ${
                          typeColors[type] || 'bg-gray-400'
                        }`}
                      >
                        {capitalizeFirst(type)}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Forte contro */}
              <div>
                <h4 className="text-lg font-semibold text-orange-700 mb-3 flex items-center gap-2">
                  <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                  Forte contro (2x danno)
                </h4>
                <div className="flex flex-wrap gap-2">
                  {typeEffectiveness.strongAgainst.length > 0 ? (
                    typeEffectiveness.strongAgainst.map((type, index) => (
                      <span
                        key={index}
                        className={`px-3 py-1 rounded-full text-white text-sm font-medium ${
                          typeColors[type] || 'bg-gray-400'
                        }`}
                      >
                        {capitalizeFirst(type)}
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-500 text-sm">Nessun vantaggio particolare</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'evoluzioni':
        return (
          <div className="evolution-tab">
            <h3 className="text-2xl font-semibold mb-6 text-center text-poke-blue">Catena Evolutiva</h3>

            {evolutions.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-dark-grey text-lg">Questo Pokémon non ha evoluzioni conosciute.</p>
              </div>
            ) : (
              <div className="evolution-chain-container">
                {/* Visualizzazione della catena evolutiva in formato orizzontale */}
                <div className="flex items-center justify-center gap-4 overflow-x-auto pb-4">
                  {evolutions.map((evolution, index) => (
                    <div key={index} className="flex items-center gap-4">
                      {/* Card dell'evoluzione */}
                      <div className={`evolution-card bg-soft-white rounded-lg p-4 shadow-md border-2 transition-all duration-300 min-w-[280px] ${
                        evolution.isCurrent
                          ? 'border-battle-red bg-red-50 ring-2 ring-battle-red ring-opacity-50'
                          : 'border-accent-sky hover:border-poke-blue'
                      }`}>
                        {/* Badge per Pokemon corrente */}
                        {evolution.isCurrent && (
                          <div className="text-center mb-2">
                            <span className="bg-battle-red text-white px-3 py-1 rounded-full text-sm font-bold">
                              Pokémon Corrente
                            </span>
                          </div>
                        )}

                        <div className="text-center">
                          {/* Sprite dell'evoluzione */}
                          <div className="evolution-sprite mb-3">
                            {evolution.sprite ? (
                              <img
                                src={evolution.sprite}
                                alt={evolution.displayName}
                                className="w-20 h-20 object-contain mx-auto"
                              />
                            ) : (
                              <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center mx-auto">
                                <span className="text-gray-500 text-sm">No Image</span>
                              </div>
                            )}
                          </div>

                          {/* Nome Pokemon */}
                          <h4 className="text-lg font-semibold text-poke-blue mb-2">
                            <Link
                              href={`/pokemon/${evolution.pokemonId}`}
                              className="hover:text-battle-red transition-colors duration-200"
                            >
                              {evolution.displayName}
                            </Link>
                          </h4>

                          {/* ID Pokemon */}
                          <div className="text-sm text-gray-600 mb-3">
                            #{evolution.pokemonId.toString().padStart(3, '0')}
                          </div>

                          {/* Dettagli evoluzione */}
                          <div className="evolution-details space-y-1 text-sm">
                            <div className="evolution-trigger">
                              <span className="text-dark-grey font-medium">Metodo: </span>
                              <span className="text-battle-red font-semibold">{evolution.trigger}</span>
                            </div>

                            {evolution.minLevel && (
                              <div className="evolution-level">
                                <span className="text-electric-yellow font-bold bg-dark-grey px-2 py-1 rounded text-xs">
                                  Lv. {evolution.minLevel}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Freccia di evoluzione (non mostrarla dopo l'ultimo elemento) */}
                      {index < evolutions.length - 1 && (
                        <div className="evolution-arrow flex-shrink-0">
                          <div className="w-10 h-10 bg-electric-yellow rounded-full flex items-center justify-center">
                            <span className="text-xl">→</span>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Informazioni aggiuntive */}
                <div className="mt-6 text-center">
                  <p className="text-sm text-gray-600">
                    Clicca su un Pokémon per visualizzare i suoi dettagli
                  </p>
                </div>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-soft-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-poke-blue mx-auto mb-4"></div>
          <p className="text-dark-grey text-xl font-body">Caricamento Pokemon...</p>
        </div>
      </div>
    );
  }

  if (error || !pokemon) {
    return (
      <div className="min-h-screen bg-soft-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-battle-red text-xl mb-4 font-body">{error || 'Pokemon non trovato'}</p>
          <Link href="/" className="bg-poke-blue hover:bg-poke-blue/90 text-white px-6 py-3 rounded-full transition-all duration-300 font-body">
            Torna alla ricerca
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-soft-white">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Card principale del Pokemon */}
        <div className="bg-white rounded-2xl shadow-xl mb-8 overflow-hidden border border-gray-100">
          <div className="grid md:grid-cols-2 gap-8 p-8">
            {/* Immagine e info base */}
            <div className="text-center">
              <div className="relative w-48 h-48 mx-auto mb-6 bg-gray-50 rounded-full flex items-center justify-center">
                {pokemon.sprites.front_default ? (
                  <Image
                    src={pokemon.sprites.front_default}
                    alt={pokemon.name}
                    width={192}
                    height={192}
                    className="pixelated"
                    style={{ imageRendering: 'pixelated' }}
                  />
                ) : (
                  <div className="text-gray-400 text-6xl">?</div>
                )}
              </div>
              <h1 className="font-title text-4xl font-bold text-dark-grey mb-2">
                {capitalizeFirst(pokemon.name)}
              </h1>
              <p className="text-gray-600 text-lg font-body">#{pokemon.id.toString().padStart(3, '0')}</p>
            </div>

            {/* Tipi e informazioni rapide */}
            <div className="flex flex-col justify-center space-y-6">
              <div>
                <h2 className="font-title text-xl font-semibold text-dark-grey mb-3">Tipi</h2>
                <div className="flex gap-2">
                  {pokemon.types.map((type, index) => (
                    <span
                      key={index}
                      className={`px-4 py-2 rounded-full text-white font-medium font-body ${
                        typeColors[type.type.name] || 'bg-gray-400'
                      }`}
                    >
                      {capitalizeFirst(type.type.name)}
                    </span>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-dark-grey font-title">
                    {(pokemon.height / 10).toFixed(1)} m
                  </div>
                  <div className="text-sm text-gray-600 font-body">Altezza</div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-dark-grey font-title">
                    {(pokemon.weight / 10).toFixed(1)} kg
                  </div>
                  <div className="text-sm text-gray-600 font-body">Peso</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Version Group Selector - Mostrato solo per tab specifici */}
        {(activeTab === 'moves' || activeTab === 'encounters') && versionGroups.length > 0 && (
          <div className="bg-white rounded-2xl shadow-lg mb-4 p-4 border border-gray-100">
            <div className="flex items-center justify-center gap-3">
              <label htmlFor="version-group" className="text-sm font-semibold text-dark-grey whitespace-nowrap font-body">
                Versione gioco:
              </label>
              <select
                id="version-group"
                value={selectedVersionGroup}
                onChange={(e) => setSelectedVersionGroup(e.target.value)}
                className="px-4 py-2 border-2 border-gray-200 rounded-lg bg-white text-dark-grey font-body focus:outline-none focus:border-poke-blue focus:ring-2 focus:ring-accent-sky/20 min-w-[200px] transition-all duration-300"
              >
                {versionGroups.map((vg) => (
                  <option key={vg.name} value={vg.name}>
                    {vg.displayName}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}

        {/* Sistema di Tab */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
          {/* Header dei Tab */}
          <div className="border-b border-gray-200">
            <div className="flex overflow-x-auto scrollbar-hide">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-4 text-sm font-medium whitespace-nowrap transition-colors border-b-2 font-body ${
                    activeTab === tab.id
                      ? 'border-poke-blue text-poke-blue bg-accent-sky/10'
                      : 'border-transparent text-gray-500 hover:text-dark-grey hover:bg-gray-50'
                  }`}
                >
                  <span className="text-lg">{tab.icon}</span>
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Contenuto del Tab */}
          <div className="p-8">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
}
