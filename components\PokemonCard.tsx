'use client';

import Image from 'next/image';
import Link from 'next/link';

interface Pokemon {
  id: number;
  name: string;
  types: Array<{
    slot: number;
    type: {
      name: string;
      url: string;
    };
  }>;
  sprites: {
    front_default: string;
  };
}

interface PokemonCardProps {
  pokemon: Pokemon;
}

// Mappa dei colori per i tipi di Pokemon
const typeColors: { [key: string]: string } = {
  normal: 'bg-gray-400',
  fire: 'bg-red-500',
  water: 'bg-blue-500',
  electric: 'bg-yellow-400',
  grass: 'bg-green-500',
  ice: 'bg-blue-200',
  fighting: 'bg-red-700',
  poison: 'bg-purple-500',
  ground: 'bg-yellow-600',
  flying: 'bg-indigo-400',
  psychic: 'bg-pink-500',
  bug: 'bg-green-400',
  rock: 'bg-yellow-800',
  ghost: 'bg-purple-700',
  dragon: 'bg-indigo-700',
  dark: 'bg-gray-800',
  steel: 'bg-gray-500',
  fairy: 'bg-pink-300',
};

const PokemonCard: React.FC<PokemonCardProps> = ({ pokemon }) => {
  const capitalizeFirst = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  return (
    <Link href={`/pokemon/${pokemon.id}`}>
      <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 overflow-hidden border border-gray-100 cursor-pointer group animate-fadeInUp">
        {/* Header con ID */}
        <div className="bg-gradient-to-r from-poke-blue to-accent-sky p-3">
          <span className="text-white text-sm font-bold font-body">
            #{pokemon.id.toString().padStart(3, '0')}
          </span>
        </div>

        {/* Contenuto principale */}
        <div className="p-6">
          {/* Immagine del Pokemon */}
          <div className="relative w-24 h-24 mx-auto mb-4 bg-gray-50 rounded-full flex items-center justify-center group-hover:bg-accent-sky/10 transition-colors">
            {pokemon.sprites.front_default ? (
              <Image
                src={pokemon.sprites.front_default}
                alt={pokemon.name}
                width={96}
                height={96}
                className="pixelated transform group-hover:scale-110 transition-transform duration-300"
                style={{ imageRendering: 'pixelated' }}
              />
            ) : (
              <div className="text-gray-400 text-4xl">?</div>
            )}
          </div>

          {/* Nome del Pokemon */}
          <h3 className="font-title text-xl font-bold text-dark-grey text-center mb-3">
            {capitalizeFirst(pokemon.name)}
          </h3>

          {/* Tipi del Pokemon */}
          <div className="flex flex-wrap gap-2 justify-center">
            {pokemon.types.map((type) => (
              <span
                key={type.slot}
                className={`px-3 py-1 rounded-full text-white text-sm font-medium font-body ${
                  typeColors[type.type.name] || 'bg-gray-400'
                }`}
              >
                {capitalizeFirst(type.type.name)}
              </span>
            ))}
          </div>
        </div>

        {/* Footer con effetto hover */}
        <div className="px-6 pb-4">
          <div className="text-center">
            <span className="text-poke-blue font-body text-sm font-medium group-hover:text-accent-sky transition-colors">
              Visualizza dettagli →
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default PokemonCard;
