@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700;800&display=swap');

:root {
  --background: #F5F5F5;
  --foreground: #222831;
  --electric-yellow: #FFCC00;
  --poke-blue: #3366CC;
  --battle-red: #CC0000;
  --soft-white: #F5F5F5;
  --dark-grey: #222831;
  --accent-sky: #7EC8E3;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Nunito', sans-serif;
}

.font-title {
  font-family: 'Fredoka', sans-serif;
}

.font-body {
  font-family: 'Nunito', sans-serif;
}

.pixelated {
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-crisp-edges;
  image-rendering: pixelated;
  image-rendering: crisp-edges;
}

/* Nascondi la scrollbar per i tab su mobile */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar { 
  display: none;  /* Safari and Chrome */
}

/* Colori personalizzati */
.bg-electric-yellow { background-color: var(--electric-yellow); }
.bg-poke-blue { background-color: var(--poke-blue); }
.bg-battle-red { background-color: var(--battle-red); }
.bg-soft-white { background-color: var(--soft-white); }
.bg-dark-grey { background-color: var(--dark-grey); }
.bg-accent-sky { background-color: var(--accent-sky); }

.text-electric-yellow { color: var(--electric-yellow); }
.text-poke-blue { color: var(--poke-blue); }
.text-battle-red { color: var(--battle-red); }
.text-soft-white { color: var(--soft-white); }
.text-dark-grey { color: var(--dark-grey); }
.text-accent-sky { color: var(--accent-sky); }

.border-electric-yellow { border-color: var(--electric-yellow); }
.border-poke-blue { border-color: var(--poke-blue); }
.border-battle-red { border-color: var(--battle-red); }
.border-soft-white { border-color: var(--soft-white); }
.border-dark-grey { border-color: var(--dark-grey); }
.border-accent-sky { border-color: var(--accent-sky); }

/* Animazioni per le card */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.3s ease-out;
}
