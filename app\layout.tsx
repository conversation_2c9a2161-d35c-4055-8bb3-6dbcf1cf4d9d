import type { Metada<PERSON> } from "next";
import "./globals.css";
import { LanguageProvider } from "@/contexts/LanguageContext";
import TopBar from "@/components/TopBar";

export const metadata: Metadata = {
  title: "PokéCatalog - Il tuo Pokédex completo",
  description: "Cerca e scopri tutti i tuoi Pokemon preferiti con informazioni dettagliate",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link 
          href="https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700;800&display=swap" 
          rel="stylesheet" 
        />
      </head>
      <body className="antialiased font-body bg-soft-white text-dark-grey min-h-screen">
        <LanguageProvider>
          <TopBar />
          <main className="min-h-screen">
            {children}
          </main>
        </LanguageProvider>
      </body>
    </html>
  );
}
